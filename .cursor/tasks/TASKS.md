# Roguelike Game Project Tasks

This document contains an index of all tasks for the roguelike game project.

## Open Tasks

| Task ID | Description | Status | Start Date | End Date |
|---------|-------------|--------|------------|----------|
| TASK-2023-04-08-01 | Implement monster AI movement patterns | 📝 Open | 2023-04-08 | - |
| TASK-2023-04-08-02 | Add player melee combat system | 📝 Open | 2023-04-08 | - |
| TASK-2023-04-08-03 | Implement field of view updates on movement | 📝 Open | 2023-04-08 | - |

## Active Tasks

| Task ID | Description | Status | Start Date | End Date |
|---------|-------------|--------|------------|----------|
| TASK-2023-04-08-04 | Debug turn queue processing issues | 🔄 Active | 2023-04-08 | - |
| TASK-2023-04-08-05 | Refactor model update function | 🔄 Active | 2023-04-08 | - |

## Completed Tasks

| Task ID | Description | Status | Start Date | End Date |
|---------|-------------|--------|------------|----------|
| TASK-2023-04-07-01 | Set up basic game loop structure | ✅ Done | 2023-04-07 | 2023-04-07 |
| TASK-2023-04-07-02 | Implement simple map generation | ✅ Done | 2023-04-07 | 2023-04-07 |
| TASK-2023-04-07-03 | Create entity component system | ✅ Done | 2023-04-07 | 2023-04-08 |
| TASK-2023-04-08-06 | Implement basic message log | ✅ Done | 2023-04-08 | 2023-04-08 |
