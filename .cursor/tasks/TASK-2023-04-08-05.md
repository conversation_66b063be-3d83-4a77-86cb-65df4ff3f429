# TASK-2023-04-08-05: Refactor model update function

## Description

The current model update function in `internal/game/model_update.go` has some issues with game state management and turn processing. This task involves refactoring the function to improve clarity, maintainability, and fix potential bugs in the turn queue processing.

## Relevant Specifications

- [Game Core](../specs/core/game_core.md)
- [Turn Queue System](../specs/core/turn_queue.md)

## Acceptance Criteria

- [ ] Refactor the update function to have a clearer separation of player input and turn processing
- [ ] Fix bug where turn queue processing might continue when it should wait for player input
- [ ] Ensure proper state handling when transitioning between player and monster turns
- [ ] Add appropriate logging for debugging turn processing
- [ ] Implement proper redraw logic when monster moves are processed

## Implementation Notes

The main issue appears to be in the `update` function where the logic for handling player input and processing the turn queue may not properly coordinate. The function should:

1. Check if we're waiting for player input
2. If yes, process player input first, then handle turn queue
3. If no, process the turn queue, then check if we need player input
4. Always redraw the screen when needed

Current buggy behavior:

- Sometimes monsters move without the screen updating
- Player input occasionally gets swallowed or delayed
- Turn queue processing sometimes continues when it should wait for player

## Metadata

- **ID**: TASK-2023-04-08-05
- **Start Date**: 2023-04-08
- **End Date**: -
- **State**: 🔄 Active
