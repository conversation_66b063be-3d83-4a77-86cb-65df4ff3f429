# Roguelike Game Project Specifications

This document contains an index of all specifications for the roguelike game project.

## Core Systems

- [Game Core](core/game_core.md) - Main game loop and state management
- [Entity Component System](core/ecs.md) - ECS architecture and implementation
- [Turn Queue System](core/turn_queue.md) - Turn-based gameplay mechanics
- [Map Generation](core/map_generation.md) - Procedural dungeon generation

## Gameplay Features

- [Movement & Combat](gameplay/movement_combat.md) - Player and monster movement and combat
- [Field of View](gameplay/fov.md) - Visibility and exploration mechanics
- [Entity Spawning](gameplay/spawning.md) - Entity creation and placement

## UI

- [Rendering](ui/rendering.md) - Game display and visual elements
- [Input Handling](ui/input_handling.md) - Player input and controls
- [Message Log](ui/message_log.md) - Game feedback and messaging system

## Completion Status

| Specification | Status | Requirements Complete |
|---------------|--------|------------------------|
| Game Core | 🔄 In Progress | 5/8 |
| Entity Component System | 🔄 In Progress | 7/10 |
| Turn Queue System | 🔄 In Progress | 4/6 |
| Map Generation | 🔄 In Progress | 3/5 |
| Movement & Combat | 🔄 In Progress | 2/6 |
| Field of View | 🔄 In Progress | 3/4 |
| Entity Spawning | 🔄 In Progress | 3/5 |
| Rendering | 🔄 In Progress | 4/6 |
| Input Handling | 🔄 In Progress | 2/4 |
| Message Log | 🔄 In Progress | 2/3 |
