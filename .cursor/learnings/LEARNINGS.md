# Roguelike Game Project Learnings

This document contains an index of all learnings captured during the development of the roguelike game project.

## Architecture

| Learning ID | Title | Date | Keywords |
|-------------|-------|------|----------|
| LEARN-2023-04-07-01 | ECS Design Patterns | 2023-04-07 | architecture, ecs, components, systems |
| LEARN-2023-04-07-02 | Turn-Based Design Considerations | 2023-04-07 | turn-based, game-loop, time-management |

## Performance

| Learning ID | Title | Date | Keywords |
|-------------|-------|------|----------|
| LEARN-2023-04-08-01 | FOV Calculation Optimization | 2023-04-08 | performance, fov, visibility, optimization |

## Debugging

| Learning ID | Title | Date | Keywords |
|-------------|-------|------|----------|
| LEARN-2023-04-08-02 | Turn Queue Debugging Patterns | 2023-04-08 | debugging, turn-queue, state-management |

## Implementation Techniques

| Learning ID | Title | Date | Keywords |
|-------------|-------|------|----------|
| LEARN-2023-04-08-03 | Effective Map Generation in Go | 2023-04-08 | procedural-generation, maps, algorithms |
| LEARN-2023-04-08-04 | Managing Game State with gruid | 2023-04-08 | gruid, state-management, ui |
