# LEARN-2023-04-08-02: Turn Queue Debugging Patterns

## Description

During development of the turn queue system, we encountered several challenges with managing turn-based gameplay. This learning document captures effective debugging patterns and solutions for turn-based game management in Go.

## Detailed Content

### Key Challenges

1. **Turn Queue Visibility**: The turn queue's state was opaque during runtime, making it difficult to understand why certain entities weren't being processed.

2. **State Synchronization**: The game state and turn queue sometimes became desynchronized, especially after entity death or removal.

3. **Input/Processing Timing**: Determining when to wait for player input versus process NPC turns was complicated by the event-driven nature of the UI framework.

### Effective Solutions

#### Enhanced Logging

The most effective debugging approach was implementing structured, level-based logging:

```go
// Debug turn queue processing
logrus.WithFields(logrus.Fields{
    "queueLength": len(g.turnQueue.entries),
    "currentTime": g.turnQueue.CurrentTime,
    "waitingForPlayer": g.waitingForInput,
}).Debug("Processing turn queue")

// For each entity processed
logrus.WithFields(logrus.Fields{
    "entityID": entry.EntityID,
    "time": entry.Time,
}).Debug("Processing entity turn")
```

#### State Assertion

Adding runtime assertions helped catch state corruption early:

```go
func (g *Game) processTurnQueue() {
    utils.Assert(g.turnQueue != nil, "Turn queue is nil")

    // Further assertions about game state
    if g.PlayerID != 0 {
        utils.Assert(g.ecs.HasComponent(g.PlayerID, components.CTurnActor),
            "Player entity missing TurnActor component")
    }

    // Process turns...
}
```

#### Debug Visualization

We added a debug overlay to visualize the turn queue state:

```go
// In debug rendering mode, show the next 5 entries in the turn queue
if config.Config.DebugLogging {
    for i, entry := range g.turnQueue.PeekNextN(5) {
        // Display entity ID, time, and other relevant info
        md.DrawText(1, grid.Height()-i-2, fmt.Sprintf("Queue %d: Entity %d at time %d",
            i, entry.EntityID, entry.Time), ui.ColorDebug)
    }
}
```

## Related Content

- [Task: Debug turn queue processing issues](../tasks/TASK-2023-04-08-04.md)
- [Specification: Turn Queue System](../specs/core/turn_queue.md)

## Learnings & Takeaways

1. Always maintain visibility into the state of time-based systems during development.

2. Use structured logging that includes all relevant state variables.

3. Implement runtime assertions to catch state corruption early.

4. Create visual debugging tools when working with complex state systems.

5. Design clear separation between input handling and state processing.

## Metadata

- **ID**: LEARN-2023-04-08-02
- **Date**: 2023-04-08
- **Keywords**: debugging, turn-queue, state-management, logging, assertions
- **Related Files**:
  - `internal/game/model_update.go`
  - `internal/turn_queue/turn_queue.go`
  - `internal/game/game.go`
